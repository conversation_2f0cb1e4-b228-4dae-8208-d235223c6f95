import 'package:flutter/material.dart';
import 'package:k_chart_plus/k_chart_plus.dart';

class Vol<PERSON>ender<PERSON> extends BaseChartRenderer<VolumeEntity> {
  late double mVolWidth;
  final ChartStyle chartStyle;
  final ChartColors chartColors;
  final String? locale;

  VolRenderer(
    Rect mainRect,
    double maxValue,
    double minValue,
    double topPadding,
    int fixedLength,
    this.chartStyle,
    this.chartColors,
    this.locale,
  ) : super(
          chartRect: mainRect,
          maxValue: maxValue,
          minValue: minValue,
          topPadding: topPadding,
          fixedLength: fixedLength,
          gridColor: chartColors.gridColor,
        ) {
    mVolWidth = chartStyle.volWidth;
  }

  @override
  void drawChart(
    VolumeEntity lastPoint,
    VolumeEntity curPoint,
    double lastX,
    double curX,
    Size size,
    Canvas canvas,
  ) {
    double r = mVolWidth / 2;
    double top = getVolY(curPoint.vol);
    double bottom = chartRect.bottom;
    if (curPoint.vol != 0) {
      canvas.drawRect(
        Rect.fromLTRB(curX - r, top, curX + r, bottom),
        chartPaint..color = curPoint.close > curPoint.open ? chartColors.upColor : chartColors.dnColor,
      );
    }

    // Draw MA volume lines - ensure they show on every chart consistently
    // MA5 Volume Line
    if (lastPoint.MA5Volume != null &&
        curPoint.MA5Volume != null &&
        lastPoint.MA5Volume! > 0 &&
        curPoint.MA5Volume! > 0) {
      drawLine(lastPoint.MA5Volume, curPoint.MA5Volume, canvas, lastX, curX, chartColors.ma5Color);
    }

    // MA10 Volume Line
    if (lastPoint.MA10Volume != null &&
        curPoint.MA10Volume != null &&
        lastPoint.MA10Volume! > 0 &&
        curPoint.MA10Volume! > 0) {
      drawLine(lastPoint.MA10Volume, curPoint.MA10Volume, canvas, lastX, curX, chartColors.ma10Color);
    }

    // MA30 Volume Line
    if (lastPoint.MA30Volume != null &&
        curPoint.MA30Volume != null &&
        lastPoint.MA30Volume! > 0 &&
        curPoint.MA30Volume! > 0) {
      drawLine(lastPoint.MA30Volume, curPoint.MA30Volume, canvas, lastX, curX, chartColors.ma30Color);
    }

    // MA60 Volume Line
    if (lastPoint.MA60Volume != null &&
        curPoint.MA60Volume != null &&
        lastPoint.MA60Volume! > 0 &&
        curPoint.MA60Volume! > 0) {
      drawLine(lastPoint.MA60Volume, curPoint.MA60Volume, canvas, lastX, curX, chartColors.ma60Color);
    }
  }

  double getVolY(double value) => (maxValue - value) * (chartRect.height / maxValue) + chartRect.top;

  @override
  void drawLine(double? lastPrice, double? curPrice, Canvas canvas, double lastX, double curX, Color color) {
    if (lastPrice == null || curPrice == null || lastPrice <= 0 || curPrice <= 0) return;

    double lastY = getVolY(lastPrice);
    double curY = getVolY(curPrice);

    // Create a separate paint for each line to avoid overlapping issues
    Paint linePaint = Paint()
      ..color = color
      ..strokeWidth = 1.5 // Slightly thicker for better visibility
      ..style = PaintingStyle.stroke
      ..isAntiAlias = true;

    canvas.drawLine(
      Offset(lastX, lastY),
      Offset(curX, curY),
      linePaint,
    );
  }

  @override
  void drawText(Canvas canvas, VolumeEntity data, double x) {
    List<TextSpan> textSpans = [
      TextSpan(
        text: "VOL:${NumberUtil.formatLargeNumber(data.vol, locale)}    ",
        style: getTextStyle(chartColors.volColor),
      ),
    ];

    // Always show MA5 and MA10 if available
    if (data.MA5Volume.notNullOrZero) {
      textSpans.add(TextSpan(
        text: "MA5:${NumberUtil.formatLargeNumber(data.MA5Volume!, locale)}    ",
        style: getTextStyle(chartColors.ma5Color),
      ));
    }

    if (data.MA10Volume.notNullOrZero) {
      textSpans.add(TextSpan(
        text: "MA10:${NumberUtil.formatLargeNumber(data.MA10Volume!, locale)}    ",
        style: getTextStyle(chartColors.ma10Color),
      ));
    }

    // Always show MA30 and MA60 if available - these should appear consistently
    if (data.MA30Volume.notNullOrZero) {
      textSpans.add(TextSpan(
        text: "MA30:${NumberUtil.formatLargeNumber(data.MA30Volume!, locale)}    ",
        style: getTextStyle(chartColors.ma30Color),
      ));
    }

    if (data.MA60Volume.notNullOrZero) {
      textSpans.add(TextSpan(
        text: "MA60:${NumberUtil.formatLargeNumber(data.MA60Volume!, locale)}    ",
        style: getTextStyle(chartColors.ma60Color),
      ));
    }

    TextSpan span = TextSpan(children: textSpans);
    TextPainter tp = TextPainter(text: span, textDirection: TextDirection.ltr);
    tp.layout();
    tp.paint(canvas, Offset(x, chartRect.top - topPadding));
  }

  @override
  void drawVerticalText(canvas, textStyle, int gridRows) {
    TextSpan span = TextSpan(text: NumberUtil.format(maxValue, locale), style: textStyle);
    TextPainter tp = TextPainter(text: span, textDirection: TextDirection.ltr);
    tp.layout();
    tp.paint(
      canvas,
      Offset(chartRect.width - tp.width, chartRect.top - topPadding),
    );
  }

  @override
  void drawGrid(Canvas canvas, int gridRows, int gridColumns) {
    canvas.drawLine(
      Offset(0, chartRect.bottom),
      Offset(chartRect.width, chartRect.bottom),
      gridPaint,
    );
    double columnSpace = chartRect.width / gridColumns;
    for (int i = 0; i <= columnSpace; i++) {
      //vol垂直线
      canvas.drawLine(
        Offset(columnSpace * i, chartRect.top - topPadding),
        Offset(columnSpace * i, chartRect.bottom),
        gridPaint,
      );
    }
  }
}
