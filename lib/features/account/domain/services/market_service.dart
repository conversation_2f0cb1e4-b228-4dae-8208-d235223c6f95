import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/company_info/company_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/dist_flow/dist_flow_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/market_status/market_status_response.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';

class MarketService {
  Future<ResponseResult<StockKlineResponse>> getKlineDetailList(String instrument, String period, String type) async {
    try {
      final Response response = await NetworkProvider().get(
        '/market/$type',
        queryParameters: {
          'period': period,
          'instrument': instrument,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = StockKlineResponse.fromJson(response.data);

        // Check if the API returned empty data (common case where WebSocket will provide data later)
        if (result.data == null || (result.data?.list == null || result.data!.list!.isEmpty)) {
          // Return success with empty data - this is not an error, WebSocket will provide data
          return ResponseResult(data: result);
        }

        return ResponseResult(data: result);
      } else {
        return ResponseResult(error: 'Failed to fetch kline data');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  Future<ResponseResult<MarketStatusResponse>> getMarketStatus({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '/market/status',
        queryParameters: {
          'market': market,
          'symbol': symbol,
          'securityType': securityType,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = MarketStatusResponse.fromJson(response.data);
        return ResponseResult(data: result);
      } else {
        return ResponseResult(error: 'Failed to fetch market status');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  Future<ResponseResult<CompanyInfoResponse>> getCompanyInfo({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '/market/companyInfo',
        queryParameters: {
          'market': market,
          'symbol': symbol,
          'securityType': securityType,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = CompanyInfoResponse.fromJson(response.data);
        return ResponseResult(data: result);
      } else {
        return ResponseResult(error: 'Failed to fetch company info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  Future<ResponseResult<DistFlowResponse>> getDistFlow({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '/market/distFlow',
        queryParameters: {
          'market': market,
          'symbol': symbol,
          'securityType': securityType,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = DistFlowResponse.fromJson(response.data);
        return ResponseResult(data: result);
      } else {
        return ResponseResult(error: 'Failed to fetch company info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
