import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart' hide MainState;
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:k_chart_plus/chart_style.dart';
import 'package:k_chart_plus/entity/k_line_entity.dart';
import 'package:k_chart_plus/k_chart_widget.dart';
import 'package:k_chart_plus/renderer/main_renderer.dart';
import 'package:k_chart_plus/utils/data_util.dart';

class TradeBottomsheetOrders extends StatelessWidget {
  const TradeBottomsheetOrders({
    super.key,
    required this.data,
    this.isTradeDetails = false,
    this.isTrading = true,
    this.contract,
  });

  final OrderRecord data;
  final bool isTradeDetails;
  final bool isTrading;

  /// Pass contract inorder to pass to trading center screen
  final ContractSummaryData? contract;
  @override
  Widget build(BuildContext context) {
    final tradeDirection = context.read<TradingCubit>().state.tradeDirection;
    final tradeDirectionLabel = tradeDirection == TradeDirection.buy ? 'buy'.tr() : 'sell'.tr();
    final tradeDirectionColor = tradeDirection == TradeDirection.buy ? ColorPalette.redColor : ColorPalette.greenColor;
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 10.gh, horizontal: 12.gw),
      decoration: BoxDecoration(
        color: context.appTheme.cardColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.gh),
          topRight: Radius.circular(10.gh),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        spacing: 8,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            spacing: 5.gh,
            children: [
              SymbolChip(
                name: MarketSymbol.getMarketType(data.market ?? 'SZSE'),
                chipColor: ColorPalette.primaryColor,
              ),
              Text(data.symbolName ?? '', style: FontPalette.normal14),
              Text(
                '(${data.symbol ?? ''})',
                style: FontPalette.light13,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                    title: 'entrustmentPrice'.tr(),
                    value: '${data.tradePrice?.toStringAsFixed(2) ?? 0.00}',
                    fontSize: 13.gr),
              ),
              Expanded(
                child: AmountRow(
                  title: 'marketPrice'.tr(),
                  value: '${data.stockPrice?.toStringAsFixed(2) ?? 0.00}',
                  fontSize: 13.gr,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                    title: 'completed'.tr(), value: '${data.dealNum?.toStringAsFixed(2) ?? 0.00}', fontSize: 13.gr),
              ),
              Expanded(
                child: AmountRow(
                    title: 'total'.tr(), value: '${data.tradeNum?.toStringAsFixed(2) ?? 0.00}', fontSize: 13.gr),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                  title: 'entrustDirection'.tr(),
                  value: tradeDirectionLabel,
                  fontSize: 13.gr,
                  color: tradeDirectionColor,
                ),
              ),
              Expanded(
                child: AmountRow(
                  title: 'status'.tr(),
                  value: EntrustStatus.fromValueByValue(data.status ?? 0).label.tr(),
                  fontSize: 13.gr,
                  color: EntrustStatus.fromValueByValue(data.status ?? 0).color,
                ),
              ),
            ],
          ),
          AmountRow(
            title: 'entrustTime'.tr(),
            value: '${data.tradeTime}',
            fontSize: 13.gr,
          ),
          Row(
            spacing: 8,
            children: [
              Expanded(
                flex: 2,
                child: AbsorbPointer(
                  child: BlocSelector<TradingCubit, TradingState, (DataStatus, StockKlineResponse?, KlineOption?)>(
                    selector: (state) => (state.klineDetailListStatus, state.klineDetailList, state.klineOption),
                    builder: (context, state) {
                      if (state.$1 == DataStatus.loading && state.$2?.data?.list == null) {
                        return ShimmerWidget(height: 180.gh);
                      }
                      if (state.$1 == DataStatus.failed || state.$2?.data?.list == null) {
                        return Center(child: Icon(Icons.error));
                      }
                      final isLine = state.$3?.type == "timeLine";
                      final scaleX = switch (state.$3?.id) {
                        "weekly-kline" => 0.5,
                        "monthly-kline" => 0.5,
                        "yearly-kline" => 1.0,
                        "intraday" => 0.15,
                        "5day" => 0.03,
                        _ => 0.8,
                      };

                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(8, 0, 8, 4),
                              child: FutureBuilder<List<KLineEntity>>(
                                future: _processData(state.$2?.data?.list, isLine),
                                builder: (context, snapshot) {
                                  if (snapshot.connectionState == ConnectionState.waiting) {
                                    return ShimmerWidget(height: 180.gh);
                                  }
                                  if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
                                    return SizedBox();
                                  }

                                  return Stack(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                                        child: KChartWidget(
                                          snapshot.data!,
                                          ChartStyle(),
                                          ChartColors(
                                            upColor: const Color(0xFFD2544F),
                                            dnColor: const Color(0xFF5DAF78),
                                            gridColor: Colors.transparent,
                                            bgColor: context.appTheme.cardColor,
                                            // Ensure MA colors are properly set for volume indicators
                                            ma5Color: const Color(0xffE5B767),
                                            ma10Color: const Color(0xff1FD1AC),
                                            ma30Color: const Color(0xffB48CE3),
                                            ma60Color: const Color(0xFFD5405D),
                                          ),
                                          getColorCallback: (value) => value.getValueColor(context),
                                          mBaseHeight: 0.27.gsh,
                                          isTrendLine: false,
                                          scaleX: scaleX,
                                          mainState: MainState.MA,
                                          volHidden: false,
                                          isTapShowInfoDialog: true,
                                          secondaryStateLi: {},
                                          timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
                                          verticalTextAlignment: VerticalTextAlignment.right,
                                          isLine: isLine,
                                          xFrontPadding: 0,
                                          locale: context.locale.languageCode,
                                        ),
                                      ),
                                      if (state.$1 == DataStatus.loading)
                                        Opacity(
                                          opacity: 0.8,
                                          child: ShimmerWidget(height: 0.34.gsh),
                                        ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
              Theme(
                data: Theme.of(context).copyWith(
                    textButtonTheme: TextButtonThemeData(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 4),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    alignment: Alignment.centerLeft,
                    foregroundColor: context.appTheme.primaryColor,
                    iconColor: context.appTheme.primaryColor,
                    textStyle: FontPalette.normal14.copyWith(color: context.appTheme.primaryColor),
                  ),
                )),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (!isTradeDetails)
                      TextButton.icon(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (_) => BlocProvider.value(
                                value: context.read<TradingCubit>(),
                                child: CancelOrderDialog(orderId: data.id!),
                              ),
                            ).then((value) {
                              if (value == true && context.mounted) {
                                Navigator.pop(context);
                              }
                            });
                          },
                          label: Text(
                            'revoke'.tr(),
                          ),
                          icon: Icon(LucideIcons.undo_2)),
                    TextButton.icon(
                      onPressed: () {
                        if (isTrading) {
                          context.read<TradingCubit>().setTradeType(TradeTabType.Quotes);
                          Navigator.pop(context);
                        } else {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                            context,
                            routeTradingCenter,
                            arguments: TradingArguments(
                              instrumentInfo: data.instrumentInfo,
                              selectedIndex: 1,
                              isIndexTrading: data.isIndex,
                            ),
                          );
                        }
                      },
                      label: Text(
                        'quote'.tr(),
                      ),
                      icon: Icon(LucideIcons.chart_no_axes_combined),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        final securityType = SecurityType.fromCode(int.tryParse(data.securityType ?? '1') ?? 1);
                        return switch (securityType) {
                          SecurityType.stocks => {
                              Navigator.pop(context),
                              Navigator.pushNamed(
                                context,
                                routeTradingCenter,
                                arguments: TradingArguments(
                                  instrumentInfo: data.instrumentInfo,
                                  selectedIndex: 0,
                                  isIndexTrading: data.isIndex,
                                  contract: contract,
                                ),
                              )
                            },
                          SecurityType.indexx => {
                              Navigator.popUntil(context, (route) => route.isFirst),
                              context.read<MarketCubit>().updateMainHeaderTab(1),
                              context.read<MainCubit>().selectedNavigationItem(NavigationItem.trade),
                              context.read<IndexTradeCubit>().updateSelectedIndex(
                                    context
                                        .read<IndexTradeCubit>()
                                        .state
                                        .indexes
                                        .indexWhere((element) => element.instrument == data.instrument),
                                    animate: true,
                                  )
                            },
                          _ => debugPrint(''),
                        };
                      },
                      label: Text(
                        'trade2'.tr(),
                      ),
                      icon: Icon(LucideIcons.receipt_text),
                    ),
                    if (isTradeDetails && !isTrading)
                      TextButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, routeOrderDetail, arguments: {
                            'orderId': data.id,
                          });
                        },
                        label: Text(
                          'details'.tr(),
                        ),
                        icon: Icon(Icons.description_outlined),
                      ),
                  ],
                ),
              )
            ],
          ),
          SizedBox(height: 10.gh),
        ],
      ),
    );
  }

  Future<List<KLineEntity>> _processData(List<KlineItem>? list, bool isLine) async {
    if (list == null) return [];

    List<KLineEntity> klineEntities = [];
    double? previousPrice;

    for (var item in list) {
      final openPrice = previousPrice ?? (isLine ? item.price ?? 0 : item.open ?? 0);
      final klineEntity = KLineEntity.fromCustom(
        time: item.time != null ? item.time! * 1000 : 0,
        close: isLine ? item.price ?? 0 : item.close ?? 0,
        open: openPrice,
        high: isLine ? item.price ?? 0 : item.high ?? 0,
        low: isLine ? item.price ?? 0 : item.low ?? 0,
        vol: item.volume ?? 0,
        amount: item.price ?? 0,
      );

      klineEntities.add(klineEntity);
      previousPrice = item.price ?? 0;
    }

    DataUtil.calculate(klineEntities);
    return klineEntities;
  }
}

class CancelOrderDialog extends StatelessWidget {
  const CancelOrderDialog({super.key, required this.orderId});

  final int orderId;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('cancelOrder'.tr()),
      content: Text('cancelOrderConfirmation'.tr()),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text('cancel'.tr()),
        ),
        TextButton(
          onPressed: () {
            context.read<TradingCubit>().cancelOrder(orderId);
            Navigator.pop(context, true);
          },
          child: Text(
            'confirm'.tr(),
            style: TextStyle(color: context.appTheme.redColor),
          ),
        ),
      ],
    );
  }
}
