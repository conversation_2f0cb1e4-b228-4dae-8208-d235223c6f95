import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/kline_selector.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/tick_list_section.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/mixin/web_socket_mixin.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/web_scoket_message.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:k_chart_plus/k_chart_plus.dart';

import '../../../../../../shared/theme/my_color_scheme.dart';

/// Custom painter for empty chart skeleton
class EmptyChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.1)
      ..strokeWidth = 1;

    // Draw grid lines
    final gridSpacing = size.height / 5;
    for (int i = 1; i < 5; i++) {
      final y = gridSpacing * i;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw vertical lines
    final verticalSpacing = size.width / 6;
    for (int i = 1; i < 6; i++) {
      final x = verticalSpacing * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class KlineSection extends StatefulWidget {
  const KlineSection({super.key, required this.instrument, this.showTicks = false});

  final Instrument instrument;
  final bool showTicks;

  @override
  State<KlineSection> createState() => _KlineSectionState();
}

class _KlineSectionState extends State<KlineSection> with WebSocketMixin {
  StreamSubscription<WebSocketMessage>? _marketSubscription;
  bool isInitial = true;
  List<KLineEntity>? _lastValidChartData;
  Future<List<KLineEntity>> _processData(List<KlineItem>? list, bool isLine) async {
    try {
      if (list == null || list.isEmpty) return [];

      List<KLineEntity> klineEntities = [];
      double? previousPrice;
      //! check here
      int offset = countryTimeOffsets['CN'] ?? 0;

      for (var item in list) {
        // Skip invalid items
        if (item.time == null || item.time! <= 0) continue;

        final openPrice = previousPrice ?? (isLine ? item.price ?? 0 : item.open ?? 0);

        final adjustedTime = DateTime.fromMillisecondsSinceEpoch(
          item.time! * 1000,
          isUtc: true,
        ).add(Duration(hours: offset)).millisecondsSinceEpoch;

        final klineEntity = KLineEntity.fromCustom(
          time: adjustedTime,
          close: isLine ? item.price ?? 0 : item.close ?? 0,
          open: openPrice,
          high: isLine ? item.price ?? 0 : item.high ?? 0,
          low: isLine ? item.price ?? 0 : item.low ?? 0,
          vol: item.volume ?? 0,
          amount: item.price ?? 0,
        );

        klineEntities.add(klineEntity);
        previousPrice = item.price ?? 0;
      }

      if (klineEntities.isEmpty) return [];

      try {
        DataUtil.calculate(klineEntities);
      } catch (e) {
        logDev("DataUtil.calculate error: $e", "KlineSection");
        // Return entities without calculation if DataUtil fails
      }

      return klineEntities;
    } catch (e) {
      logDev("_processData error: $e", "KlineSection");
      return [];
    }
  }

  void _listenToSocketUpdates() {
    // Listen for market data updates
    _marketSubscription = onMessage(SocketEvents.market).listen(_handleMarketUpdate);

    // Subscribe to timeline data for this instrument
    webSocketService.send({
      'type': SocketEvents.market,
      'action': SocketActions.timeLine,
      'params': {
        'instrument': widget.instrument.instrument,
        'period': 'day',
        'operate': 'subscribe',
      }
    });
  }

  void _handleMarketUpdate(WebSocketMessage message) {
    // Validate response code
    if (message.data['code'] != 200) return;

    // Parse message data
    final stockMarketUpdate = StockKlineResponse.fromJson(message.data);
    logDev("Socket message data: ${message.data}", "KlineSection");
    logDev("Parsed stockMarketUpdate: $stockMarketUpdate", "KlineSection");
    logDev("stockMarketUpdate.data: ${stockMarketUpdate.data}", "KlineSection");
    if (stockMarketUpdate.data == null) return;

    // Check if this update is for our instrument
    final isMatchingInstrument = stockMarketUpdate.data?.detail?.instrument == widget.instrument.instrument;
    logDev(
        "Instrument check: ${stockMarketUpdate.data?.detail?.instrument} == ${widget.instrument.instrument} = $isMatchingInstrument",
        "KlineSection");
    if (!isMatchingInstrument) return;

    // Check if market is open
    final isMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(stockMarketUpdate.data?.detail?.market);
    logDev("Market status check: ${stockMarketUpdate.data?.detail?.market} isOpen = $isMarketOpen", "KlineSection");
    if (!isMarketOpen) return;

    final tradingCubit = context.read<TradingCubit>();
    final currentKlineData = tradingCubit.state.klineDetailList;

    if (currentKlineData != null && currentKlineData.data != null) {
      // Create updated kline data by merging existing data with new data
      final updatedKlineData = currentKlineData.copyWith(
        data: currentKlineData.data!.copyWith(
          list: [
            ...?currentKlineData.data!.list?.where((e) => e.time != stockMarketUpdate.data?.list?.last.time),
            ...?stockMarketUpdate.data?.list
          ],
          detail: stockMarketUpdate.data?.detail,
        ),
      );

      if (mounted) {
        tradingCubit.updateKlineDetailList(updatedKlineData);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<TradingCubit>().getKlineDetailList(widget.instrument.instrument, KlineConstants.options[0]);
    _listenToSocketUpdates();
  }

  @override
  void dispose() {
    _marketSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.appTheme.cardColor,
      child: Column(
        children: [
          KlineSelector(instrument: widget.instrument.instrument),
          BlocSelector<TradingCubit, TradingState, (DataStatus, StockKlineResponse?, KlineOption?)>(
            selector: (state) => (state.klineDetailListStatus, state.klineDetailList, state.klineOption),
            builder: (context, state) {
              if (state.$1 == DataStatus.loading && state.$2?.data?.list == null) {
                return ShimmerWidget(height: 180.gh);
              }
              // Only show error for actual failures, not empty data
              if (state.$1 == DataStatus.failed) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, color: Colors.red),
                      SizedBox(height: 8),
                      Text("chart_failed_to_load".tr(), style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                );
              }
              final isLine = state.$3?.type == "timeLine";
              final showTicks = state.$3?.id == "intraday" && widget.showTicks;
              final scaleX = switch (state.$3?.id) {
                "weekly-kline" => 0.5,
                "monthly-kline" => 0.5,
                "yearly-kline" => 1.0,
                "intraday" => 0.15,
                "5day" => 0.03,
                _ => 0.8,
              };

              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(8, 0, 8, 4),
                      child: FutureBuilder<List<KLineEntity>>(
                        future: _processData(state.$2?.data?.list, isLine),
                        builder: (context, snapshot) {
                          // Show shimmer only on initial load or when data is null
                          if (snapshot.connectionState == ConnectionState.waiting &&
                              (isInitial || state.$2?.data?.list == null)) {
                            return ShimmerWidget(height: 180.gh);
                          }

                          // More specific error handling
                          if (snapshot.hasError) {
                            logDev("FutureBuilder error: ${snapshot.error}", "KlineSection");
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.error, color: Colors.red),
                                  Text("chart_data_error".tr(), style: TextStyle(fontSize: 12, color: Colors.grey)),
                                ],
                              ),
                            );
                          }

                          // Check if we have valid data
                          if (!snapshot.hasData || snapshot.data == null || snapshot.data!.isEmpty) {
                            if (state.$1 != DataStatus.loading && state.$2?.data?.list != null) {
                              logDev("No chart data available but API data exists", "KlineSection");
                            }

                            // Try to use last valid chart data if available
                            if (_lastValidChartData != null && _lastValidChartData!.isNotEmpty) {
                              logDev("Using last valid chart data while waiting for new data", "KlineSection");
                              final closePrice = state.$2?.data?.detail?.close;

                              return Stack(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                                    child: KChartWidget(
                                      _lastValidChartData!,
                                      ChartStyle(),
                                      ChartColors(
                                        upColor: const Color(0xFFD2544F),
                                        dnColor: const Color(0xFF5DAF78),
                                        gridColor: Colors.transparent,
                                        bgColor: context.appTheme.cardColor,
                                        // Ensure MA colors are properly set for volume indicators
                                        ma5Color: const Color(0xffE5B767),
                                        ma10Color: const Color(0xff1FD1AC),
                                        ma30Color: const Color(0xffB48CE3),
                                        ma60Color: const Color(0xFFD5405D),
                                      ),
                                      key: ValueKey(
                                          'cached_${state.$3?.id}_${state.$3?.type}_${_lastValidChartData!.length}'), // Force rebuild on chart type change
                                      getColorCallback: (value) => value.getValueColor(context),
                                      mBaseHeight: 0.27.gsh,
                                      isTrendLine: false,
                                      scaleX: scaleX,
                                      mainState: MainState.MA,
                                      volHidden: false,
                                      isTapShowInfoDialog: true,
                                      secondaryStateLi: {},
                                      timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
                                      verticalTextAlignment: VerticalTextAlignment.right,
                                      isLine: isLine,
                                      xFrontPadding: 0,
                                      closePrice: closePrice,
                                      showDate: !showTicks,
                                      locale: context.locale.languageCode,
                                      isMarketOpen:
                                          getIt<MarketStatusCubit>().isMarketOpen(state.$2?.data?.detail?.market),
                                    ),
                                  ),
                                  // Show subtle loading overlay
                                  if (state.$1 == DataStatus.loading)
                                    Container(
                                      color: context.appTheme.cardColor.withValues(alpha: 0.7),
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 20,
                                              height: 20,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor: AlwaysStoppedAnimation<Color>(
                                                  context.appTheme.primaryColor,
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 8),
                                            Text(
                                              "chart_loading".tr(),
                                              style: TextStyle(fontSize: 12, color: Colors.grey),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                ],
                              );
                            }

                            // Show empty chart skeleton if no previous data
                            return SizedBox(
                              height: 180.gh,
                              child: Stack(
                                children: [
                                  // Empty chart background
                                  Container(
                                    width: double.infinity,
                                    height: double.infinity,
                                    decoration: BoxDecoration(
                                      color: context.appTheme.cardColor,
                                      border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
                                    ),
                                    child: CustomPaint(
                                      painter: EmptyChartPainter(),
                                    ),
                                  ),
                                  // Loading indicator if still loading
                                  if (state.$1 == DataStatus.loading)
                                    Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(
                                                context.appTheme.primaryColor,
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 8),
                                          Text(
                                            "chart_loading".tr(),
                                            style: TextStyle(fontSize: 12, color: Colors.grey),
                                          ),
                                        ],
                                      ),
                                    ),
                                  // Show "Waiting for data" if not loading but no data
                                  if (state.$1 != DataStatus.loading)
                                    Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.timeline, color: Colors.grey.withValues(alpha: 0.5), size: 32),
                                          SizedBox(height: 8),
                                          Text(
                                            "chart_waiting_for_data".tr(),
                                            style: TextStyle(fontSize: 12, color: Colors.grey),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            );
                          }

                          isInitial = false;

                          // Store valid chart data for future use
                          _lastValidChartData = snapshot.data;

                          final closePrice = state.$2?.data?.detail?.close;

                          return Stack(
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                                child: KChartWidget(
                                  snapshot.data!,
                                  ChartStyle(),
                                  ChartColors(
                                    upColor: const Color(0xFFD2544F),
                                    dnColor: const Color(0xFF5DAF78),
                                    gridColor: Colors.transparent,
                                    bgColor: context.appTheme.cardColor,
                                    // Ensure MA colors are properly set for volume indicators
                                    ma5Color: const Color(0xffE5B767),
                                    ma10Color: const Color(0xff1FD1AC),
                                    ma30Color: const Color(0xffB48CE3),
                                    ma60Color: const Color(0xFFD5405D),
                                  ),
                                  key: ValueKey(
                                      '${state.$3?.id}_${state.$3?.type}_${snapshot.data!.length}'), // Force rebuild on chart type change
                                  getColorCallback: (value) => value.getValueColor(context),
                                  mBaseHeight: 0.27.gsh,
                                  isTrendLine: false,
                                  scaleX: scaleX,
                                  mainState: MainState.MA,
                                  volHidden: false,
                                  isTapShowInfoDialog: true,
                                  secondaryStateLi: {},
                                  timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
                                  verticalTextAlignment: VerticalTextAlignment.right,
                                  isLine: isLine,
                                  xFrontPadding: 0,
                                  closePrice: closePrice,
                                  showDate: !showTicks,
                                  locale: context.locale.languageCode,
                                  isMarketOpen: getIt<MarketStatusCubit>().isMarketOpen(state.$2?.data?.detail?.market),
                                ),
                              ),
                              if (state.$1 == DataStatus.loading)
                                Opacity(
                                  opacity: 0.8,
                                  child: ShimmerWidget(height: 0.34.gsh),
                                ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                  if (showTicks)
                    TickListSection(
                        instrument: widget.instrument, getColorCallback: (value) => value.getValueColor(context)),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
