pipeline {
    agent any

    environment {
        LANG = 'zh_CN.UTF-8'
        LC_ALL = 'zh_CN.UTF-8'
        FLUTTER_VERSION = '3.29.3'
        FIXED_GIT_URL = 'http://************:8090/front/gp-stock-app.git'
        credentialsId = '553e5a24-910c-47fb-8c13-a3045779f662'
        TELEGRAM_CHAT_ID = -4737563269
    }

    parameters {
        gitParameter(
                name: 'GIT_BRANCH',
                type: 'PT_BRANCH',
                defaultValue: 'dev',
                description: '选择要构建的 Git 分支/标签',
                branchFilter: 'origin/(.*)',
                selectedValue: 'DEFAULT',
                sortMode: 'ASCENDING_SMART'
        )
        choice(name: 'platform', choices: ['all', 'android', 'ios'], description: '构建平台')
        choice(name: 'flavor', choices: ['gp', 'pre', 'rsyp', 'yhxt'], description: '选择需打包的项目\ngp：GP Stock\npre: GP 预发布环境\nrsyp：荣顺优配\nyhxt:沅和信投')
        choice(name: 'environment', choices: ['release', 'debug'], description: '构建环境')
        string(name: 'buildVersionName', defaultValue: '', description: '自定义版本号（可选）')
    }


    stages {
        stage('init PATH') {
            steps {
                script {
                    env.PATH = "/opt/homebrew/lib/ruby/gems/3.4.0/bin:" +
                            "/opt/homebrew/bin:" +
                            "/Users/<USER>/dev/flutter/bin:" +
                            env.PATH
                }
                sh 'echo PATH=$PATH'
                sh 'which sh'
                sh 'which fvm'
                sh 'which flutter'
            }
        }

        stage('拉取代码') {
            steps {
                git branch: "${params.GIT_BRANCH}",
                        credentialsId: "${env.credentialsId}",
                        url: "${env.FIXED_GIT_URL}"
            }
        }

        stage('初始化 & 获取版本号') {
            steps {
                script {
                    sh "fvm use ${FLUTTER_VERSION}"

                    env.ACTUAL_FLAVOR = params.flavor
                    env.ACTUAL_ENVIRONMENT = params.environment
                    env.GIT_SHA1 = sh(script: "git rev-parse --short HEAD", returnStdout: true).trim()

                    sh "fvm flutter clean"
                    sh "./scripts/switch_flavor.sh ${ACTUAL_FLAVOR}"

                    def versionLine = sh(script: "grep 'version:' pubspec.yaml", returnStdout: true).trim()
                    def versionParts = versionLine.replace("version:", "").trim().split("\\+")
                    env.VERSION_NAME = params.buildVersionName?.trim() ?: versionParts[0]
                    env.VERSION_CODE = versionParts.length > 1 ? versionParts[1] : "1"
                }

                echo "🔢 版本: ${VERSION_NAME} + ${VERSION_CODE}"
                echo "🌱 分支: ${params.GIT_BRANCH}, 🎯 环境: ${ACTUAL_ENVIRONMENT}, 🎮 FLAVOR: ${ACTUAL_FLAVOR}"
            }
        }

        stage('构建 Android') {
            when {
                expression { params.platform == 'android' || params.platform == 'all' }
            }
            steps {
                sh """
                  fvm flutter build apk \
                    --flavor ${ACTUAL_FLAVOR} \
                    -t lib/main_${ACTUAL_FLAVOR}.dart \
                    --${ACTUAL_ENVIRONMENT} \
                    --obfuscate \
                    --split-debug-info=xx \
                    --build-name=${VERSION_NAME} \
                    --build-number=${VERSION_CODE}
                """
            }
        }

        stage('归档 APK') {
            when {
                expression { params.platform == 'android' || params.platform == 'all' }
            }
            steps {
                script {
                    def archiveDir = "${env.HOME}/Desktop/Archive/${ACTUAL_FLAVOR}/${ACTUAL_ENVIRONMENT}"
                    def oldApkDir = "${archiveDir}/old_apk"
                    def buildApkPath = "build/app/outputs/flutter-apk/app-${ACTUAL_FLAVOR}-${ACTUAL_ENVIRONMENT}.apk"
                    def newApkName = "${ACTUAL_FLAVOR}_${VERSION_NAME}_b${VERSION_CODE}_${GIT_SHA1}_${ACTUAL_ENVIRONMENT}.apk"

                    sh """
                        mkdir -p "${oldApkDir}" "${archiveDir}"
                        if ls "${archiveDir}"/*.apk 1> /dev/null 2>&1; then
                          mv "${archiveDir}"/*.apk "${oldApkDir}/"
                        fi
                        if [[ -f "${buildApkPath}" ]]; then
                          mv "${buildApkPath}" "${archiveDir}/${newApkName}"
                          echo "✅ APK 文件已归档：${newApkName}"
                        else
                          echo "❌ 未找到 APK 文件"
                          exit 1
                        fi
                    """
                }
            }
        }

        stage('构建 iOS') {
            when {
                expression { params.platform == 'ios' || params.platform == 'all' }
            }
            steps {
                sh """
                  fvm flutter build ios \
                    --flavor ${ACTUAL_FLAVOR} \
                    -t lib/main_${ACTUAL_FLAVOR}.dart \
                    --release \
                    --build-name=${VERSION_NAME} \
                    --build-number=${VERSION_CODE} \
                    --no-codesign
        
                  xcodebuild -workspace ios/Runner.xcworkspace \
                    -scheme ${ACTUAL_FLAVOR} \
                    -sdk iphoneos \
                    -configuration Release-${ACTUAL_FLAVOR} \
                    -archivePath build/Runner.xcarchive \
                    archive \
                    CODE_SIGN_IDENTITY="iPhone Distribution: Kerim BOLAT (84DQM7K34Q)"
                    PROVISIONING_PROFILE_SPECIFIER=""
        
                  xcodebuild -exportArchive \
                    -archivePath build/Runner.xcarchive \
                    -exportPath build/Runner \
                    -exportOptionsPlist ios/CI/exportOptions_${ACTUAL_FLAVOR}.plist
                """
            }
        }

        stage('归档 IPA') {
            when {
                expression { params.platform == 'ios' || params.platform == 'all' }
            }
            steps {
                script {
                    def archiveDir = "${env.HOME}/Desktop/Archive/${ACTUAL_FLAVOR}/${ACTUAL_ENVIRONMENT}"
                    def oldIpaDir = "${archiveDir}/old_ipa"
                    def ipaFile = sh(script: "find build/Runner -name '*.ipa' | head -n 1", returnStdout: true).trim()
                    def newIpaName = "${ACTUAL_FLAVOR}_${VERSION_NAME}_b${VERSION_CODE}_${GIT_SHA1}_${ACTUAL_ENVIRONMENT}.ipa"

                    sh """
                        mkdir -p "${oldIpaDir}" "${archiveDir}"
                        if ls "${archiveDir}"/*.ipa 1> /dev/null 2>&1; then
                          mv "${archiveDir}"/*.ipa "${oldIpaDir}/"
                        fi
                        if [[ -f "${ipaFile}" ]]; then
                          mv "${ipaFile}" "${archiveDir}/${newIpaName}"
                          echo "✅ IPA 文件已归档：${newIpaName}"
                        else
                          echo "❌ 未找到 IPA 文件"
                          exit 1
                        fi
                    """
                }
            }
        }

        stage('内部分发') {
            steps {
                script {
                    def output = sh(
                            script: """
                                set -e
            
                                chmod +x scripts/internal_testing_page/*.sh
            
                                export buildVersionName=${params.buildVersionName}
                                export flavor=${params.flavor}
                                export environment=${params.environment}
            
                                scripts/internal_testing_page/generate_manifest.sh
                                scripts/internal_testing_page/build_internal_distribution.sh
                            """,
                            returnStdout: true
                    ).trim()

                    def version = output.readLines().find { it.contains('[BUILD_VERSION]') }
                    env.BUILD_VERSION = version?.replace('[BUILD_VERSION]', '')?.trim()

                    def urlLine = output.readLines().find { it.contains('[BUILD_OUTPUT_URL]') }
                    env.DOWNLOAD_URL = urlLine?.replace('[BUILD_OUTPUT_URL]', '')?.trim()

                    echo "✅ 下载地址：${env.DOWNLOAD_URL}"
                }
            }
        }

    }

    post {
        always {
            httpRequest(
                    url: 'https://4ebf-94-207-19-32.ngrok-free.app/jenkins_callback',
                    httpMode: 'POST',
                    contentType: 'APPLICATION_JSON',
                    requestBody: groovy.json.JsonOutput.toJson([
                            project: params.flavor,
                            result : currentBuild.currentResult,
                            version : env.BUILD_VERSION,
                            chat_id: env.TELEGRAM_CHAT_ID,
                            download_url: env.DOWNLOAD_URL
                    ])
            )
        }
        success {
            echo "🎉 构建成功：平台 ${params.platform}，flavor ${params.flavor}"
        }
        failure {
            echo "🚨 构建失败！请检查日志"
        }
    }
}
